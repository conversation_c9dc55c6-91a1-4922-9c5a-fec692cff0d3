<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o Car.out -mCar.map -iD:/TI/CCS/mspm0_sdk_2_05_00_05/source -iD:/Projects/TI/Car -iD:/Projects/TI/Car/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=Car_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./led_app.o ./scheduler.o ./APP/oled_app.o ./BSP/ENCODER/encoder.o ./BSP/OLED/oled_hardware_i2c.o ./System/clock.o ./System/interrupt.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x6867f3f0</link_time>
   <link_errors>0x0</link_errors>
   <output_file>D:\Projects\TI\Car\Debug\Car.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x10ad</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\Projects\TI\Car\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\Projects\TI\Car\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\Projects\TI\Car\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\Projects\TI\Car\Debug\.\</path>
         <kind>object</kind>
         <file>led_app.o</file>
         <name>led_app.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\Projects\TI\Car\Debug\.\</path>
         <kind>object</kind>
         <file>scheduler.o</file>
         <name>scheduler.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\Projects\TI\Car\Debug\.\APP\</path>
         <kind>object</kind>
         <file>oled_app.o</file>
         <name>oled_app.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\Projects\TI\Car\Debug\.\BSP\ENCODER\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>D:\Projects\TI\Car\Debug\.\BSP\OLED\</path>
         <kind>object</kind>
         <file>oled_hardware_i2c.o</file>
         <name>oled_hardware_i2c.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>D:\Projects\TI\Car\Debug\.\System\</path>
         <kind>object</kind>
         <file>clock.o</file>
         <name>clock.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>D:\Projects\TI\Car\Debug\.\System\</path>
         <kind>object</kind>
         <file>interrupt.o</file>
         <name>interrupt.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\Projects\TI\Car\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-1a">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.OLED_ShowNum</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x490</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-97">
         <name>.text.OLED_Init</name>
         <load_address>0x550</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x550</run_address>
         <size>0x2c0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0x810</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x810</run_address>
         <size>0x134</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x944</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x944</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.text.OLED_ShowChar</name>
         <load_address>0xa48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa48</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.SYSCFG_DL_PWM_DRIVE_init</name>
         <load_address>0xb24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb24</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0xc00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc00</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text:memcpy</name>
         <load_address>0xca0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xca0</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0xd3a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd3a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0xd3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd3c</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0xdb8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdb8</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0xe16</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe16</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text.SYSCFG_DL_I2C_MPU6050_init</name>
         <load_address>0xe18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe18</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0xe6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe6c</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xec0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec0</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xf14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf14</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text.scheduler_run</name>
         <load_address>0xf5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf5c</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0xfa0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfa0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xfe0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfe0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.mspm0_delay_ms</name>
         <load_address>0x101c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x101c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x1050</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1050</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1080</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1080</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x10ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10ac</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x10d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10d4</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text.Get_Speed</name>
         <load_address>0x10fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10fc</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.oled_task</name>
         <load_address>0x1120</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1120</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x1140</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1140</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x115c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x115c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text.main</name>
         <load_address>0x1178</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1178</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1194</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1194</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.Pid_Cal</name>
         <load_address>0x11ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11ac</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.mspm0_get_clock_ms</name>
         <load_address>0x11c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11c4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x11dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11dc</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x11f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11f4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x1208</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1208</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x121c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x121c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x122c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x122c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.led_task</name>
         <load_address>0x123c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x123c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.text.scheduler_init</name>
         <load_address>0x1248</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1248</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1254</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1254</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x1260</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1260</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text:abort</name>
         <load_address>0x1268</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1268</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.HOSTexit</name>
         <load_address>0x126e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x126e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1272</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1272</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text._system_pre_init</name>
         <load_address>0x1276</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1276</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-178">
         <name>.cinit..data.load</name>
         <load_address>0x1aa8</load_address>
         <readonly>true</readonly>
         <run_address>0x1aa8</run_address>
         <size>0x1f</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-176">
         <name>__TI_handler_table</name>
         <load_address>0x1ac8</load_address>
         <readonly>true</readonly>
         <run_address>0x1ac8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-179">
         <name>.cinit..bss.load</name>
         <load_address>0x1ad4</load_address>
         <readonly>true</readonly>
         <run_address>0x1ad4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-177">
         <name>__TI_cinit_table</name>
         <load_address>0x1adc</load_address>
         <readonly>true</readonly>
         <run_address>0x1adc</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-132">
         <name>.rodata.asc2_1608</name>
         <load_address>0x1280</load_address>
         <readonly>true</readonly>
         <run_address>0x1280</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-133">
         <name>.rodata.asc2_0806</name>
         <load_address>0x1870</load_address>
         <readonly>true</readonly>
         <run_address>0x1870</run_address>
         <size>0x228</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.rodata.gPWM_DRIVEConfig</name>
         <load_address>0x1a98</load_address>
         <readonly>true</readonly>
         <run_address>0x1a98</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.rodata.gPWM_DRIVEClockConfig</name>
         <load_address>0x1aa0</load_address>
         <readonly>true</readonly>
         <run_address>0x1aa0</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-102">
         <name>.rodata.gI2C_MPU6050ClockConfig</name>
         <load_address>0x1aa3</load_address>
         <readonly>true</readonly>
         <run_address>0x1aa3</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-101">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x1aa5</load_address>
         <readonly>true</readonly>
         <run_address>0x1aa5</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-140">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-d9">
         <name>.data.scheduler_task</name>
         <load_address>0x202000cc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000cc</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.data.sys_tick</name>
         <load_address>0x202000f0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000f0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.common:gPWM_DRIVEBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-d6">
         <name>.common:task_num</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c8</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-85">
         <name>.common:Coder_CounterA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000bc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-123">
         <name>.common:EncoderA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-103">
         <name>.common:start_time</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000c4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x63</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_abbrev</name>
         <load_address>0x63</load_address>
         <run_address>0x63</run_address>
         <size>0x25f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x2c2</load_address>
         <run_address>0x2c2</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_abbrev</name>
         <load_address>0x32f</load_address>
         <run_address>0x32f</run_address>
         <size>0xe5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_abbrev</name>
         <load_address>0x414</load_address>
         <run_address>0x414</run_address>
         <size>0xe6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_abbrev</name>
         <load_address>0x4fa</load_address>
         <run_address>0x4fa</run_address>
         <size>0x7e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_abbrev</name>
         <load_address>0x578</load_address>
         <run_address>0x578</run_address>
         <size>0x110</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_abbrev</name>
         <load_address>0x688</load_address>
         <run_address>0x688</run_address>
         <size>0x294</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0x91c</load_address>
         <run_address>0x91c</run_address>
         <size>0xc4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x9e0</load_address>
         <run_address>0x9e0</run_address>
         <size>0x123</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_abbrev</name>
         <load_address>0xb03</load_address>
         <run_address>0xb03</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_abbrev</name>
         <load_address>0xb65</load_address>
         <run_address>0xb65</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_abbrev</name>
         <load_address>0xd4c</load_address>
         <run_address>0xd4c</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0xfd2</load_address>
         <run_address>0xfd2</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_abbrev</name>
         <load_address>0x1081</load_address>
         <run_address>0x1081</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_abbrev</name>
         <load_address>0x11f1</load_address>
         <run_address>0x11f1</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_abbrev</name>
         <load_address>0x122a</load_address>
         <run_address>0x122a</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x12ec</load_address>
         <run_address>0x12ec</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0x135c</load_address>
         <run_address>0x135c</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_abbrev</name>
         <load_address>0x13e9</load_address>
         <run_address>0x13e9</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_abbrev</name>
         <load_address>0x1481</load_address>
         <run_address>0x1481</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_abbrev</name>
         <load_address>0x14ad</load_address>
         <run_address>0x14ad</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_abbrev</name>
         <load_address>0x14d4</load_address>
         <run_address>0x14d4</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_abbrev</name>
         <load_address>0x14fb</load_address>
         <run_address>0x14fb</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_abbrev</name>
         <load_address>0x1554</load_address>
         <run_address>0x1554</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_abbrev</name>
         <load_address>0x1579</load_address>
         <run_address>0x1579</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_info</name>
         <load_address>0xac</load_address>
         <run_address>0xac</run_address>
         <size>0x32a0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x334c</load_address>
         <run_address>0x334c</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_info</name>
         <load_address>0x33cc</load_address>
         <run_address>0x33cc</run_address>
         <size>0x6fd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_info</name>
         <load_address>0x3ac9</load_address>
         <run_address>0x3ac9</run_address>
         <size>0x134</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_info</name>
         <load_address>0x3bfd</load_address>
         <run_address>0x3bfd</run_address>
         <size>0x88</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_info</name>
         <load_address>0x3c85</load_address>
         <run_address>0x3c85</run_address>
         <size>0x739</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_info</name>
         <load_address>0x43be</load_address>
         <run_address>0x43be</run_address>
         <size>0x25f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_info</name>
         <load_address>0x69ae</load_address>
         <run_address>0x69ae</run_address>
         <size>0x119</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x6ac7</load_address>
         <run_address>0x6ac7</run_address>
         <size>0x233</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_info</name>
         <load_address>0x6cfa</load_address>
         <run_address>0x6cfa</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_info</name>
         <load_address>0x6d6f</load_address>
         <run_address>0x6d6f</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_info</name>
         <load_address>0x7a31</load_address>
         <run_address>0x7a31</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xaba3</load_address>
         <run_address>0xaba3</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_info</name>
         <load_address>0xafc6</load_address>
         <run_address>0xafc6</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_info</name>
         <load_address>0xb70a</load_address>
         <run_address>0xb70a</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0xb750</load_address>
         <run_address>0xb750</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0xb8e2</load_address>
         <run_address>0xb8e2</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0xb9a8</load_address>
         <run_address>0xb9a8</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_info</name>
         <load_address>0xbb24</load_address>
         <run_address>0xbb24</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_info</name>
         <load_address>0xbc1c</load_address>
         <run_address>0xbc1c</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0xbc57</load_address>
         <run_address>0xbc57</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-128">
         <name>.debug_info</name>
         <load_address>0xbdf0</load_address>
         <run_address>0xbdf0</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_info</name>
         <load_address>0xbfac</load_address>
         <run_address>0xbfac</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0xc031</load_address>
         <run_address>0xc031</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.debug_info</name>
         <load_address>0xc32b</load_address>
         <run_address>0xc32b</run_address>
         <size>0x8c</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_str</name>
         <load_address>0xfe</load_address>
         <run_address>0xfe</run_address>
         <size>0x2651</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x274f</load_address>
         <run_address>0x274f</run_address>
         <size>0x147</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-122">
         <name>.debug_str</name>
         <load_address>0x2896</load_address>
         <run_address>0x2896</run_address>
         <size>0x431</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_str</name>
         <load_address>0x2cc7</load_address>
         <run_address>0x2cc7</run_address>
         <size>0x157</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_str</name>
         <load_address>0x2e1e</load_address>
         <run_address>0x2e1e</run_address>
         <size>0xfa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_str</name>
         <load_address>0x2f18</load_address>
         <run_address>0x2f18</run_address>
         <size>0x45c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_str</name>
         <load_address>0x3374</load_address>
         <run_address>0x3374</run_address>
         <size>0xf53</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_str</name>
         <load_address>0x42c7</load_address>
         <run_address>0x42c7</run_address>
         <size>0x146</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x440d</load_address>
         <run_address>0x440d</run_address>
         <size>0x211</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_str</name>
         <load_address>0x461e</load_address>
         <run_address>0x461e</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_str</name>
         <load_address>0x478b</load_address>
         <run_address>0x478b</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_str</name>
         <load_address>0x503a</load_address>
         <run_address>0x503a</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x6e06</load_address>
         <run_address>0x6e06</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_str</name>
         <load_address>0x702b</load_address>
         <run_address>0x702b</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_str</name>
         <load_address>0x735a</load_address>
         <run_address>0x735a</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_str</name>
         <load_address>0x744f</load_address>
         <run_address>0x744f</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x75ea</load_address>
         <run_address>0x75ea</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x7752</load_address>
         <run_address>0x7752</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_str</name>
         <load_address>0x7927</load_address>
         <run_address>0x7927</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_str</name>
         <load_address>0x7a6f</load_address>
         <run_address>0x7a6f</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_str</name>
         <load_address>0x7b58</load_address>
         <run_address>0x7b58</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_frame</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_frame</name>
         <load_address>0x150</load_address>
         <run_address>0x150</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_frame</name>
         <load_address>0x170</load_address>
         <run_address>0x170</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_frame</name>
         <load_address>0x1b0</load_address>
         <run_address>0x1b0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_frame</name>
         <load_address>0x1dc</load_address>
         <run_address>0x1dc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_frame</name>
         <load_address>0x20c</load_address>
         <run_address>0x20c</run_address>
         <size>0x1e4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_frame</name>
         <load_address>0x3f0</load_address>
         <run_address>0x3f0</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x448</load_address>
         <run_address>0x448</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_frame</name>
         <load_address>0x480</load_address>
         <run_address>0x480</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_frame</name>
         <load_address>0x4a0</load_address>
         <run_address>0x4a0</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_frame</name>
         <load_address>0x5cc</load_address>
         <run_address>0x5cc</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x9d4</load_address>
         <run_address>0x9d4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_frame</name>
         <load_address>0xa64</load_address>
         <run_address>0xa64</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_frame</name>
         <load_address>0xb64</load_address>
         <run_address>0xb64</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_frame</name>
         <load_address>0xb84</load_address>
         <run_address>0xb84</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0xbbc</load_address>
         <run_address>0xbbc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0xbe4</load_address>
         <run_address>0xbe4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_frame</name>
         <load_address>0xc14</load_address>
         <run_address>0xc14</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_frame</name>
         <load_address>0xc44</load_address>
         <run_address>0xc44</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_frame</name>
         <load_address>0xc64</load_address>
         <run_address>0xc64</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xec</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_line</name>
         <load_address>0xec</load_address>
         <run_address>0xec</run_address>
         <size>0x6fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x7e6</load_address>
         <run_address>0x7e6</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_line</name>
         <load_address>0x8a2</load_address>
         <run_address>0x8a2</run_address>
         <size>0x1cd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_line</name>
         <load_address>0xa6f</load_address>
         <run_address>0xa6f</run_address>
         <size>0x142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_line</name>
         <load_address>0xbb1</load_address>
         <run_address>0xbb1</run_address>
         <size>0xf9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_line</name>
         <load_address>0xcaa</load_address>
         <run_address>0xcaa</run_address>
         <size>0x20c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0xeb6</load_address>
         <run_address>0xeb6</run_address>
         <size>0xf36</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_line</name>
         <load_address>0x1dec</load_address>
         <run_address>0x1dec</run_address>
         <size>0x19c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x1f88</load_address>
         <run_address>0x1f88</run_address>
         <size>0x20a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_line</name>
         <load_address>0x2192</load_address>
         <run_address>0x2192</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_line</name>
         <load_address>0x230a</load_address>
         <run_address>0x230a</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_line</name>
         <load_address>0x298c</load_address>
         <run_address>0x298c</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x40fa</load_address>
         <run_address>0x40fa</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_line</name>
         <load_address>0x42d6</load_address>
         <run_address>0x42d6</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_line</name>
         <load_address>0x47f0</load_address>
         <run_address>0x47f0</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0x482e</load_address>
         <run_address>0x482e</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x492c</load_address>
         <run_address>0x492c</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x49ec</load_address>
         <run_address>0x49ec</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_line</name>
         <load_address>0x4bb4</load_address>
         <run_address>0x4bb4</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_line</name>
         <load_address>0x4c1b</load_address>
         <run_address>0x4c1b</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x4c5c</load_address>
         <run_address>0x4c5c</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_line</name>
         <load_address>0x4d00</load_address>
         <run_address>0x4d00</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_line</name>
         <load_address>0x4dc2</load_address>
         <run_address>0x4dc2</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_line</name>
         <load_address>0x4e77</load_address>
         <run_address>0x4e77</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x144</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_loc</name>
         <load_address>0x144</load_address>
         <run_address>0x144</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_loc</name>
         <load_address>0x185</load_address>
         <run_address>0x185</run_address>
         <size>0xf80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_loc</name>
         <load_address>0x1105</load_address>
         <run_address>0x1105</run_address>
         <size>0x83</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_loc</name>
         <load_address>0x1188</load_address>
         <run_address>0x1188</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_loc</name>
         <load_address>0x119b</load_address>
         <run_address>0x119b</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_loc</name>
         <load_address>0x14ed</load_address>
         <run_address>0x14ed</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x2f14</load_address>
         <run_address>0x2f14</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_loc</name>
         <load_address>0x2fec</load_address>
         <run_address>0x2fec</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x3410</load_address>
         <run_address>0x3410</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x357c</load_address>
         <run_address>0x357c</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x35eb</load_address>
         <run_address>0x35eb</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_loc</name>
         <load_address>0x3752</load_address>
         <run_address>0x3752</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_loc</name>
         <load_address>0x3778</load_address>
         <run_address>0x3778</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x58</load_address>
         <run_address>0x58</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_ranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_ranges</name>
         <load_address>0x88</load_address>
         <run_address>0x88</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_ranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_ranges</name>
         <load_address>0x210</load_address>
         <run_address>0x210</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_ranges</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_ranges</name>
         <load_address>0x250</load_address>
         <run_address>0x250</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_ranges</name>
         <load_address>0x428</load_address>
         <run_address>0x428</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x600</load_address>
         <run_address>0x600</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_ranges</name>
         <load_address>0x648</load_address>
         <run_address>0x648</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_ranges</name>
         <load_address>0x690</load_address>
         <run_address>0x690</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x6a8</load_address>
         <run_address>0x6a8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_ranges</name>
         <load_address>0x6f8</load_address>
         <run_address>0x6f8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_ranges</name>
         <load_address>0x710</load_address>
         <run_address>0x710</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-df"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_ranges</name>
         <load_address>0x728</load_address>
         <run_address>0x728</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x11c0</size>
         <contents>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1aa8</load_address>
         <run_address>0x1aa8</run_address>
         <size>0x48</size>
         <contents>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-177"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x1280</load_address>
         <run_address>0x1280</run_address>
         <size>0x828</size>
         <contents>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-101"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-140"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202000cc</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-5c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0xc9</size>
         <contents>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-103"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-17b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-137" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-138" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-139" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13a" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13b" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13c" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13e" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15a" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1588</size>
         <contents>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-17d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15c" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc3b7</size>
         <contents>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-17c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15e" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7ceb</size>
         <contents>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-135"/>
         </contents>
      </logical_group>
      <logical_group id="lg-160" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc94</size>
         <contents>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-12d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-162" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4f17</size>
         <contents>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-8c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-164" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3798</size>
         <contents>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-136"/>
         </contents>
      </logical_group>
      <logical_group id="lg-166" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x750</size>
         <contents>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-89"/>
         </contents>
      </logical_group>
      <logical_group id="lg-170" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x68</size>
         <contents>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-8b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-17a" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-187" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1af0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-188" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0xf4</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-189" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1af0</used_space>
         <unused_space>0x1e510</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x11c0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1280</start_address>
               <size>0x828</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1aa8</start_address>
               <size>0x48</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1af0</start_address>
               <size>0x1e510</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x2f1</used_space>
         <unused_space>0x7d0f</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-13c"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-13e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xc9</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202000c9</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x202000cc</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202000f4</start_address>
               <size>0x7d0c</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1aa8</load_address>
            <load_size>0x1f</load_size>
            <run_address>0x202000cc</run_address>
            <run_size>0x28</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x1ad4</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xc9</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x1adc</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1aec</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1aec</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x1ac8</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x1ad4</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3a">
         <name>main</name>
         <value>0x1179</value>
         <object_component_ref idref="oc-72"/>
      </symbol>
      <symbol id="sm-62">
         <name>SYSCFG_DL_init</name>
         <value>0x1081</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-63">
         <name>SYSCFG_DL_initPower</name>
         <value>0xf15</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-64">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0xc01</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-65">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xec1</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-66">
         <name>SYSCFG_DL_PWM_DRIVE_init</name>
         <value>0xb25</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-67">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0xe6d</value>
         <object_component_ref idref="oc-cb"/>
      </symbol>
      <symbol id="sm-68">
         <name>SYSCFG_DL_I2C_MPU6050_init</name>
         <value>0xe19</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-69">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x1051</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-6a">
         <name>gPWM_DRIVEBackup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-75">
         <name>Default_Handler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-76">
         <name>Reset_Handler</name>
         <value>0x1273</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-77">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-78">
         <name>NMI_Handler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>HardFault_Handler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>SVC_Handler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>PendSV_Handler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>GROUP0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>TIMG8_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>UART3_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>ADC0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>ADC1_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>CANFD0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>DAC0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>SPI0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>SPI1_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>UART1_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>UART2_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>UART0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>TIMG0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>TIMG6_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>TIMA0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>TIMA1_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>TIMG7_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>TIMG12_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>I2C0_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>I2C1_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>AES_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>RTC_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>DMA_IRQHandler</name>
         <value>0xd3b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>led_task</name>
         <value>0x123d</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-ab">
         <name>scheduler_init</name>
         <value>0x1249</value>
         <object_component_ref idref="oc-9d"/>
      </symbol>
      <symbol id="sm-ac">
         <name>task_num</name>
         <value>0x202000c8</value>
      </symbol>
      <symbol id="sm-ad">
         <name>scheduler_run</name>
         <value>0xf5d</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-b6">
         <name>oled_task</name>
         <value>0x1121</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-c3">
         <name>Get_Speed</name>
         <value>0x10fd</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-c4">
         <name>Coder_CounterA</name>
         <value>0x202000bc</value>
      </symbol>
      <symbol id="sm-c5">
         <name>Pid_Cal</name>
         <value>0x11ad</value>
         <object_component_ref idref="oc-10a"/>
      </symbol>
      <symbol id="sm-c6">
         <name>EncoderA</name>
         <value>0x202000c0</value>
      </symbol>
      <symbol id="sm-d9">
         <name>OLED_WR_Byte</name>
         <value>0x811</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-da">
         <name>OLED_ShowChar</name>
         <value>0xa49</value>
         <object_component_ref idref="oc-12b"/>
      </symbol>
      <symbol id="sm-db">
         <name>asc2_1608</name>
         <value>0x1280</value>
         <object_component_ref idref="oc-132"/>
      </symbol>
      <symbol id="sm-dc">
         <name>asc2_0806</name>
         <value>0x1870</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-dd">
         <name>OLED_ShowNum</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-124"/>
      </symbol>
      <symbol id="sm-de">
         <name>OLED_Init</name>
         <value>0x551</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-ec">
         <name>mspm0_delay_ms</name>
         <value>0x101d</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-ed">
         <name>sys_tick</name>
         <value>0x202000f0</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-ee">
         <name>start_time</name>
         <value>0x202000c4</value>
      </symbol>
      <symbol id="sm-ef">
         <name>mspm0_get_clock_ms</name>
         <value>0x11c5</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-fc">
         <name>SysTick_Handler</name>
         <value>0x122d</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-fd">
         <name>GROUP1_IRQHandler</name>
         <value>0x11f5</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-fe">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-ff">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-100">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-101">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-102">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-103">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-104">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-105">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-106">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-10f">
         <name>DL_Common_delayCycles</name>
         <value>0x1255</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-11b">
         <name>DL_I2C_setClockConfig</name>
         <value>0x10d5</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-11c">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0xdb9</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-133">
         <name>DL_Timer_setClockConfig</name>
         <value>0x115d</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-134">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x121d</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-135">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x1141</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-136">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1195</value>
         <object_component_ref idref="oc-f6"/>
      </symbol>
      <symbol id="sm-137">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x945</value>
         <object_component_ref idref="oc-f5"/>
      </symbol>
      <symbol id="sm-142">
         <name>_c_int00_noargs</name>
         <value>0x10ad</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-143">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-14f">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xfe1</value>
         <object_component_ref idref="oc-ad"/>
      </symbol>
      <symbol id="sm-157">
         <name>_system_pre_init</name>
         <value>0x1277</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-162">
         <name>__TI_zero_init_nomemset</name>
         <value>0x11dd</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-16b">
         <name>__TI_decompress_none</name>
         <value>0x1209</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-176">
         <name>__TI_decompress_lzss</name>
         <value>0xd3d</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-180">
         <name>abort</name>
         <value>0x1269</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-18a">
         <name>HOSTexit</name>
         <value>0x126f</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-18b">
         <name>C$$EXIT</name>
         <value>0x126e</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-191">
         <name>__aeabi_memcpy</name>
         <value>0x1261</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-192">
         <name>__aeabi_memcpy4</name>
         <value>0x1261</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-193">
         <name>__aeabi_memcpy8</name>
         <value>0x1261</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-199">
         <name>__aeabi_uidiv</name>
         <value>0xfa1</value>
         <object_component_ref idref="oc-127"/>
      </symbol>
      <symbol id="sm-19a">
         <name>__aeabi_uidivmod</name>
         <value>0xfa1</value>
         <object_component_ref idref="oc-127"/>
      </symbol>
      <symbol id="sm-1a4">
         <name>__aeabi_idiv0</name>
         <value>0xe17</value>
         <object_component_ref idref="oc-12c"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>memcpy</name>
         <value>0xca1</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-1be">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c1">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c2">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
