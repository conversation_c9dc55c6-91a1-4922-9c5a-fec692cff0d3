################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"D:/Projects/TI/Car/System" -I"D:/Projects/TI/Car/BSP/ENCODER" -I"D:/Projects/TI/Car/BSP/OLED" -I"D:/Projects/TI/Car/BSP/MPU6050" -I"D:/Projects/TI/Car/APP" -I"D:/Projects/TI/Car" -I"D:/Projects/TI/Car/Debug" -I"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/TI/CCS/mspm0_sdk_2_05_00_05/source" -DMOTION_DRIVER_TARGET_MSPM0 -D__MSPM0G3507__ -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

build-983212625: ../empty.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"D:/TI/CCS/ccs/utils/sysconfig_1.24.0/sysconfig_cli.bat" --script "D:/Projects/TI/Car/empty.syscfg" -o "." -s "D:/TI/CCS/mspm0_sdk_2_05_00_05/.metadata/product.json" --compiler ticlang
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-983212625 ../empty.syscfg
device.opt: build-983212625
device.cmd.genlibs: build-983212625
ti_msp_dl_config.c: build-983212625
ti_msp_dl_config.h: build-983212625
Event.dot: build-983212625

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"D:/Projects/TI/Car/System" -I"D:/Projects/TI/Car/BSP/ENCODER" -I"D:/Projects/TI/Car/BSP/OLED" -I"D:/Projects/TI/Car/BSP/MPU6050" -I"D:/Projects/TI/Car/APP" -I"D:/Projects/TI/Car" -I"D:/Projects/TI/Car/Debug" -I"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/TI/CCS/mspm0_sdk_2_05_00_05/source" -DMOTION_DRIVER_TARGET_MSPM0 -D__MSPM0G3507__ -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"D:/Projects/TI/Car/System" -I"D:/Projects/TI/Car/BSP/ENCODER" -I"D:/Projects/TI/Car/BSP/OLED" -I"D:/Projects/TI/Car/BSP/MPU6050" -I"D:/Projects/TI/Car/APP" -I"D:/Projects/TI/Car" -I"D:/Projects/TI/Car/Debug" -I"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/TI/CCS/mspm0_sdk_2_05_00_05/source" -DMOTION_DRIVER_TARGET_MSPM0 -D__MSPM0G3507__ -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


