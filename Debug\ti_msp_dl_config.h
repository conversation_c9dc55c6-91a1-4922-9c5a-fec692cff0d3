/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_DRIVE */
#define PWM_DRIVE_INST                                                     TIMA0
#define PWM_DRIVE_INST_IRQHandler                               TIMA0_IRQHandler
#define PWM_DRIVE_INST_INT_IRQN                                 (TIMA0_INT_IRQn)
#define PWM_DRIVE_INST_CLK_FREQ                                          1000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_DRIVE_C0_PORT                                             GPIOA
#define GPIO_PWM_DRIVE_C0_PIN                                      DL_GPIO_PIN_8
#define GPIO_PWM_DRIVE_C0_IOMUX                                  (IOMUX_PINCM19)
#define GPIO_PWM_DRIVE_C0_IOMUX_FUNC                 IOMUX_PINCM19_PF_TIMA0_CCP0
#define GPIO_PWM_DRIVE_C0_IDX                                DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_DRIVE_C1_PORT                                             GPIOA
#define GPIO_PWM_DRIVE_C1_PIN                                      DL_GPIO_PIN_7
#define GPIO_PWM_DRIVE_C1_IOMUX                                  (IOMUX_PINCM14)
#define GPIO_PWM_DRIVE_C1_IOMUX_FUNC                 IOMUX_PINCM14_PF_TIMA0_CCP1
#define GPIO_PWM_DRIVE_C1_IDX                                DL_TIMER_CC_1_INDEX
/* GPIO defines for channel 2 */
#define GPIO_PWM_DRIVE_C2_PORT                                             GPIOA
#define GPIO_PWM_DRIVE_C2_PIN                                     DL_GPIO_PIN_15
#define GPIO_PWM_DRIVE_C2_IOMUX                                  (IOMUX_PINCM37)
#define GPIO_PWM_DRIVE_C2_IOMUX_FUNC                 IOMUX_PINCM37_PF_TIMA0_CCP2
#define GPIO_PWM_DRIVE_C2_IDX                                DL_TIMER_CC_2_INDEX
/* GPIO defines for channel 3 */
#define GPIO_PWM_DRIVE_C3_PORT                                             GPIOA
#define GPIO_PWM_DRIVE_C3_PIN                                     DL_GPIO_PIN_17
#define GPIO_PWM_DRIVE_C3_IOMUX                                  (IOMUX_PINCM39)
#define GPIO_PWM_DRIVE_C3_IOMUX_FUNC                 IOMUX_PINCM39_PF_TIMA0_CCP3
#define GPIO_PWM_DRIVE_C3_IDX                                DL_TIMER_CC_3_INDEX




/* Defines for I2C_OLED */
#define I2C_OLED_INST                                                       I2C0
#define I2C_OLED_INST_IRQHandler                                 I2C0_IRQHandler
#define I2C_OLED_INST_INT_IRQN                                     I2C0_INT_IRQn
#define I2C_OLED_BUS_SPEED_HZ                                             400000
#define GPIO_I2C_OLED_SDA_PORT                                             GPIOA
#define GPIO_I2C_OLED_SDA_PIN                                      DL_GPIO_PIN_0
#define GPIO_I2C_OLED_IOMUX_SDA                                   (IOMUX_PINCM1)
#define GPIO_I2C_OLED_IOMUX_SDA_FUNC                    IOMUX_PINCM1_PF_I2C0_SDA
#define GPIO_I2C_OLED_SCL_PORT                                             GPIOA
#define GPIO_I2C_OLED_SCL_PIN                                      DL_GPIO_PIN_1
#define GPIO_I2C_OLED_IOMUX_SCL                                   (IOMUX_PINCM2)
#define GPIO_I2C_OLED_IOMUX_SCL_FUNC                    IOMUX_PINCM2_PF_I2C0_SCL

/* Defines for I2C_MPU6050 */
#define I2C_MPU6050_INST                                                    I2C1
#define I2C_MPU6050_INST_IRQHandler                              I2C1_IRQHandler
#define I2C_MPU6050_INST_INT_IRQN                                  I2C1_INT_IRQn
#define I2C_MPU6050_BUS_SPEED_HZ                                          400000
#define GPIO_I2C_MPU6050_SDA_PORT                                          GPIOA
#define GPIO_I2C_MPU6050_SDA_PIN                                  DL_GPIO_PIN_10
#define GPIO_I2C_MPU6050_IOMUX_SDA                               (IOMUX_PINCM21)
#define GPIO_I2C_MPU6050_IOMUX_SDA_FUNC                IOMUX_PINCM21_PF_I2C1_SDA
#define GPIO_I2C_MPU6050_SCL_PORT                                          GPIOA
#define GPIO_I2C_MPU6050_SCL_PIN                                  DL_GPIO_PIN_11
#define GPIO_I2C_MPU6050_IOMUX_SCL                               (IOMUX_PINCM22)
#define GPIO_I2C_MPU6050_IOMUX_SCL_FUNC                IOMUX_PINCM22_PF_I2C1_SCL



/* Port definition for Pin Group LED */
#define LED_PORT                                                         (GPIOA)

/* Defines for PIN_A3: GPIOA.3 with pinCMx 8 on package pin 43 */
#define LED_PIN_A3_PIN                                           (DL_GPIO_PIN_3)
#define LED_PIN_A3_IOMUX                                          (IOMUX_PINCM8)
/* Port definition for Pin Group GPIO_MPU6050 */
#define GPIO_MPU6050_PORT                                                (GPIOA)

/* Defines for PIN_INT: GPIOA.9 with pinCMx 20 on package pin 55 */
// groups represented: ["Encoders","GPIO_MPU6050"]
// pins affected: ["E1A","PIN_INT"]
#define GPIO_MULTIPLE_GPIOA_INT_IRQN                            (GPIOA_INT_IRQn)
#define GPIO_MULTIPLE_GPIOA_INT_IIDX            (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define GPIO_MPU6050_PIN_INT_IIDX                            (DL_GPIO_IIDX_DIO9)
#define GPIO_MPU6050_PIN_INT_PIN                                 (DL_GPIO_PIN_9)
#define GPIO_MPU6050_PIN_INT_IOMUX                               (IOMUX_PINCM20)
/* Port definition for Pin Group Encoders */
#define Encoders_PORT                                                    (GPIOA)

/* Defines for E1A: GPIOA.2 with pinCMx 7 on package pin 42 */
#define Encoders_E1A_IIDX                                    (DL_GPIO_IIDX_DIO2)
#define Encoders_E1A_PIN                                         (DL_GPIO_PIN_2)
#define Encoders_E1A_IOMUX                                        (IOMUX_PINCM7)
/* Defines for E1B: GPIOA.4 with pinCMx 9 on package pin 44 */
#define Encoders_E1B_PIN                                         (DL_GPIO_PIN_4)
#define Encoders_E1B_IOMUX                                        (IOMUX_PINCM9)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_DRIVE_init(void);
void SYSCFG_DL_I2C_OLED_init(void);
void SYSCFG_DL_I2C_MPU6050_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
