/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const I2C2    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                          = "LED";
GPIO1.associatedPins[0].$name        = "PIN_A3";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[0].assignedPin  = "3";
GPIO1.associatedPins[0].pin.$assign  = "PA3";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                               = "GPIO_MPU6050";
GPIO2.associatedPins[0].$name             = "PIN_INT";
GPIO2.associatedPins[0].direction         = "INPUT";
GPIO2.associatedPins[0].internalResistor  = "PULL_UP";
GPIO2.associatedPins[0].interruptEn       = true;
GPIO2.associatedPins[0].interruptPriority = "1";
GPIO2.associatedPins[0].polarity          = "FALL";
GPIO2.associatedPins[0].pin.$assign       = "PA9";

GPIO3.$name                               = "Encoders";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name             = "E1A";
GPIO3.associatedPins[0].direction         = "INPUT";
GPIO3.associatedPins[0].assignedPort      = "PORTA";
GPIO3.associatedPins[0].assignedPin       = "2";
GPIO3.associatedPins[0].interruptEn       = true;
GPIO3.associatedPins[0].interruptPriority = "1";
GPIO3.associatedPins[0].polarity          = "RISE";
GPIO3.associatedPins[0].internalResistor  = "PULL_UP";
GPIO3.associatedPins[1].$name             = "E1B";
GPIO3.associatedPins[1].direction         = "INPUT";
GPIO3.associatedPins[1].assignedPort      = "PORTA";
GPIO3.associatedPins[1].assignedPin       = "4";
GPIO3.associatedPins[1].internalResistor  = "PULL_UP";

I2C1.$name                             = "I2C_OLED";
I2C1.basicEnableController             = true;
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.peripheral.$assign                = "I2C0";
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");

I2C2.$name                             = "I2C_MPU6050";
I2C2.basicEnableController             = true;
I2C2.basicControllerStandardBusSpeed   = "Fast";
I2C2.peripheral.sdaPin.$assign         = "PA10";
I2C2.peripheral.sclPin.$assign         = "PA11";
I2C2.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
I2C2.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C2.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C2.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C2.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";
I2C2.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C2.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C2.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");

PWM1.$name                              = "PWM_DRIVE";
PWM1.timerCount                         = 100;
PWM1.clockDivider                       = 8;
PWM1.clockPrescale                      = 4;
PWM1.timerStartTimer                    = true;
PWM1.ccIndex                            = [0,1,2,3];
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle            = 100;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.peripheral.$assign                 = "TIMA0";
PWM1.peripheral.ccp0Pin.$assign         = "PA8";
PWM1.peripheral.ccp1Pin.$assign         = "PA7";
PWM1.peripheral.ccp2Pin.$assign         = "PA15";
PWM1.peripheral.ccp3Pin.$assign         = "PA17";
PWM1.PWM_CHANNEL_2.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM1.PWM_CHANNEL_3.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM1.ccp2PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
PWM1.ccp3PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

SYSCTL.forceDefaultClkConfig = true;

SYSTICK.periodEnable      = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptEnable   = true;
SYSTICK.period            = 32000;
SYSTICK.interruptPriority = "0";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO3.associatedPins[0].pin.$suggestSolution = "PA2";
GPIO3.associatedPins[1].pin.$suggestSolution = "PA4";
I2C1.peripheral.sdaPin.$suggestSolution      = "PA0";
I2C1.peripheral.sclPin.$suggestSolution      = "PA1";
I2C2.peripheral.$suggestSolution             = "I2C1";
SYSCTL.peripheral.$suggestSolution           = "SYSCTL";
