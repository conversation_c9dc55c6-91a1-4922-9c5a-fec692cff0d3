[{"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/empty.c"}, {"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/scheduler.c"}, {"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/led_app.c"}, {"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/Debug/ti_msp_dl_config.c"}, {"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/System/clock.c"}, {"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/System/interrupt.c"}, {"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/APP/oled_app.c"}, {"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/BSP/OLED/oled_hardware_i2c.c"}, {"directory": "D:/Projects/TI/Car/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/Projects/TI/Car/System\" -I\"D:/Projects/TI/Car/BSP/ENCODER\" -I\"D:/Projects/TI/Car/BSP/OLED\" -I\"D:/Projects/TI/Car/BSP/MPU6050\" -I\"D:/Projects/TI/Car/APP\" -I\"D:/Projects/TI/Car\" -I\"D:/Projects/TI/Car/Debug\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/CCS/mspm0_sdk_2_05_00_05/source\" -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/Projects/TI/Car/BSP/ENCODER/encoder.c"}]