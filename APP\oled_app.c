#include "oled_app.h"
#include "stdio.h"

uint8_t oled_buffer[32];

void oled_task(void)
{
        // sprintf((char *)oled_buffer, "%-6.1f", pitch);
        // OLED_ShowString(5*8,0,oled_buffer,16);
        // sprintf((char *)oled_buffer, "%-6.1f", roll);
        // OLED_ShowString(5*8,2,oled_buffer,16);
        // sprintf((char *)oled_buffer, "%-6.1f", yaw);
        // OLED_ShowString(5*8,4,oled_buffer,16);

        // sprintf((char *)oled_buffer, "%6d", accel[0]);
        // OLED_ShowString(15*6,0,oled_buffer,8);
        // sprintf((char *)oled_buffer, "%6d", accel[1]);
        // OLED_ShowString(15*6,1,oled_buffer,8);
        // sprintf((char *)oled_buffer, "%6d", accel[2]);
        // OLED_ShowString(15*6,2,oled_buffer,8);

        // sprintf((char *)oled_buffer, "%6d", gyro[0]);
        // OLED_ShowString(15*6,5,oled_buffer,8);
        // sprintf((char *)oled_buffer, "%6d", gyro[1]);
        // OLED_ShowString(15*6,6,oled_buffer,8);
        // sprintf((char *)oled_buffer, "%6d", gyro[2]);
        // OLED_ShowString(15*6,7,oled_buffer,8);

        OLED_ShowNum(0, 0, EncoderA, 3, 8);
}