******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Jul  4 23:32:00 2025

OUTPUT FILE NAME:   <Car.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000010ad


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001af0  0001e510  R  X
  SRAM                  20200000   00008000  000002f1  00007d0f  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001af0   00001af0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000011c0   000011c0    r-x .text
  00001280    00001280    00000828   00000828    r-- .rodata
  00001aa8    00001aa8    00000048   00000048    r-- .cinit
20200000    20200000    000000f4   00000000    rw-
  20200000    20200000    000000c9   00000000    rw- .bss
  202000cc    202000cc    00000028   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000011c0     
                  000000c0    00000490     oled_hardware_i2c.o (.text.OLED_ShowNum)
                  00000550    000002c0     oled_hardware_i2c.o (.text.OLED_Init)
                  00000810    00000134     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  00000944    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000a48    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00000b24    000000dc     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_DRIVE_init)
                  00000c00    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000ca0    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000d3a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000d3c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000db8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00000e16    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000e18    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00000e6c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000ec0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000f14    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000f5c    00000044     scheduler.o (.text.scheduler_run)
                  00000fa0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000fe0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000101c    00000034     clock.o (.text.mspm0_delay_ms)
                  00001050    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001080    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000010ac    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000010d4    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000010fa    00000002     --HOLE-- [fill = 0]
                  000010fc    00000024     encoder.o (.text.Get_Speed)
                  00001120    00000020     oled_app.o (.text.oled_task)
                  00001140    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000115c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001178    0000001a     empty.o (.text.main)
                  00001192    00000002     --HOLE-- [fill = 0]
                  00001194    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000011ac    00000018     encoder.o (.text.Pid_Cal)
                  000011c4    00000018     clock.o (.text.mspm0_get_clock_ms)
                  000011dc    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000011f2    00000002     --HOLE-- [fill = 0]
                  000011f4    00000014     interrupt.o (.text.GROUP1_IRQHandler)
                  00001208    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  0000121a    00000002     --HOLE-- [fill = 0]
                  0000121c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000122c    00000010     interrupt.o (.text.SysTick_Handler)
                  0000123c    0000000c     led_app.o (.text.led_task)
                  00001248    0000000c     scheduler.o (.text.scheduler_init)
                  00001254    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000125e    00000002     --HOLE-- [fill = 0]
                  00001260    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001268    00000006     libc.a : exit.c.obj (.text:abort)
                  0000126e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001272    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001276    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000127a    00000006     --HOLE-- [fill = 0]

.cinit     0    00001aa8    00000048     
                  00001aa8    0000001f     (.cinit..data.load) [load image, compression = lzss]
                  00001ac7    00000001     --HOLE-- [fill = 0]
                  00001ac8    0000000c     (__TI_handler_table)
                  00001ad4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001adc    00000010     (__TI_cinit_table)
                  00001aec    00000004     --HOLE-- [fill = 0]

.rodata    0    00001280    00000828     
                  00001280    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00001870    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00001a98    00000008     ti_msp_dl_config.o (.rodata.gPWM_DRIVEConfig)
                  00001aa0    00000003     ti_msp_dl_config.o (.rodata.gPWM_DRIVEClockConfig)
                  00001aa3    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00001aa5    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00001aa7    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000000c9     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_DRIVEBackup)
                  202000bc    00000004     (.common:Coder_CounterA)
                  202000c0    00000004     (.common:EncoderA)
                  202000c4    00000004     (.common:start_time)
                  202000c8    00000001     (.common:task_num)

.data      0    202000cc    00000028     UNINITIALIZED
                  202000cc    00000024     scheduler.o (.data.scheduler_task)
                  202000f0    00000004     clock.o (.data.sys_tick)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             796    15        188    
       startup_mspm0g350x_ticlang.o   6      192       0      
       scheduler.o                    80     0         37     
       empty.o                        26     0         0      
       led_app.o                      12     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         920    207       225    
                                                              
    .\APP\
       oled_app.o                     32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         32     0         0      
                                                              
    .\BSP\ENCODER\
       encoder.o                      60     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         60     0         8      
                                                              
    .\BSP\OLED\
       oled_hardware_i2c.o            2400   2072      0      
    +--+------------------------------+------+---------+---------+
       Total:                         2400   2072      0      
                                                              
    .\System\
       clock.o                        76     0         8      
       interrupt.o                    36     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         112    0         8      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     356    0         0      
       dl_i2c.o                       132    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         498    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         74     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      67        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4528   2346      753    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001adc records: 2, size/record: 8, table size: 16
	.data: load addr=00001aa8, load size=0000001f bytes, run addr=202000cc, run size=00000028 bytes, compression=lzss
	.bss: load addr=00001ad4, load size=00000008 bytes, run addr=20200000, run size=000000c9 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001ac8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00000d3b  ADC0_IRQHandler                 
00000d3b  ADC1_IRQHandler                 
00000d3b  AES_IRQHandler                  
0000126e  C$$EXIT                         
00000d3b  CANFD0_IRQHandler               
202000bc  Coder_CounterA                  
00000d3b  DAC0_IRQHandler                 
00001255  DL_Common_delayCycles           
00000db9  DL_I2C_fillControllerTXFIFO     
000010d5  DL_I2C_setClockConfig           
00000945  DL_Timer_initFourCCPWMMode      
00001141  DL_Timer_setCaptCompUpdateMethod
00001195  DL_Timer_setCaptureCompareOutCtl
0000121d  DL_Timer_setCaptureCompareValue 
0000115d  DL_Timer_setClockConfig         
00000d3b  DMA_IRQHandler                  
00000d3b  Default_Handler                 
202000c0  EncoderA                        
00000d3b  GROUP0_IRQHandler               
000011f5  GROUP1_IRQHandler               
000010fd  Get_Speed                       
0000126f  HOSTexit                        
00000d3b  HardFault_Handler               
00000d3b  I2C0_IRQHandler                 
00000d3b  I2C1_IRQHandler                 
00000d3b  NMI_Handler                     
00000551  OLED_Init                       
00000a49  OLED_ShowChar                   
000000c1  OLED_ShowNum                    
00000811  OLED_WR_Byte                    
00000d3b  PendSV_Handler                  
000011ad  Pid_Cal                         
00000d3b  RTC_IRQHandler                  
00001273  Reset_Handler                   
00000d3b  SPI0_IRQHandler                 
00000d3b  SPI1_IRQHandler                 
00000d3b  SVC_Handler                     
00000c01  SYSCFG_DL_GPIO_init             
00000e19  SYSCFG_DL_I2C_MPU6050_init      
00000e6d  SYSCFG_DL_I2C_OLED_init         
00000b25  SYSCFG_DL_PWM_DRIVE_init        
00000ec1  SYSCFG_DL_SYSCTL_init           
00001051  SYSCFG_DL_SYSTICK_init          
00001081  SYSCFG_DL_init                  
00000f15  SYSCFG_DL_initPower             
0000122d  SysTick_Handler                 
00000d3b  TIMA0_IRQHandler                
00000d3b  TIMA1_IRQHandler                
00000d3b  TIMG0_IRQHandler                
00000d3b  TIMG12_IRQHandler               
00000d3b  TIMG6_IRQHandler                
00000d3b  TIMG7_IRQHandler                
00000d3b  TIMG8_IRQHandler                
00000d3b  UART0_IRQHandler                
00000d3b  UART1_IRQHandler                
00000d3b  UART2_IRQHandler                
00000d3b  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00001adc  __TI_CINIT_Base                 
00001aec  __TI_CINIT_Limit                
00001aec  __TI_CINIT_Warm                 
00001ac8  __TI_Handler_Table_Base         
00001ad4  __TI_Handler_Table_Limit        
00000fe1  __TI_auto_init_nobinit_nopinit  
00000d3d  __TI_decompress_lzss            
00001209  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
000011dd  __TI_zero_init_nomemset         
00000e17  __aeabi_idiv0                   
00001261  __aeabi_memcpy                  
00001261  __aeabi_memcpy4                 
00001261  __aeabi_memcpy8                 
00000fa1  __aeabi_uidiv                   
00000fa1  __aeabi_uidivmod                
ffffffff  __binit__                       
UNDEFED   __mpu_init                      
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000010ad  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00001277  _system_pre_init                
00001269  abort                           
00001870  asc2_0806                       
00001280  asc2_1608                       
ffffffff  binit                           
20200000  gPWM_DRIVEBackup                
00000000  interruptVectors                
0000123d  led_task                        
00001179  main                            
00000ca1  memcpy                          
0000101d  mspm0_delay_ms                  
000011c5  mspm0_get_clock_ms              
00001121  oled_task                       
00001249  scheduler_init                  
00000f5d  scheduler_run                   
202000c4  start_time                      
202000f0  sys_tick                        
202000c8  task_num                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  OLED_ShowNum                    
00000200  __STACK_SIZE                    
00000551  OLED_Init                       
00000811  OLED_WR_Byte                    
00000945  DL_Timer_initFourCCPWMMode      
00000a49  OLED_ShowChar                   
00000b25  SYSCFG_DL_PWM_DRIVE_init        
00000c01  SYSCFG_DL_GPIO_init             
00000ca1  memcpy                          
00000d3b  ADC0_IRQHandler                 
00000d3b  ADC1_IRQHandler                 
00000d3b  AES_IRQHandler                  
00000d3b  CANFD0_IRQHandler               
00000d3b  DAC0_IRQHandler                 
00000d3b  DMA_IRQHandler                  
00000d3b  Default_Handler                 
00000d3b  GROUP0_IRQHandler               
00000d3b  HardFault_Handler               
00000d3b  I2C0_IRQHandler                 
00000d3b  I2C1_IRQHandler                 
00000d3b  NMI_Handler                     
00000d3b  PendSV_Handler                  
00000d3b  RTC_IRQHandler                  
00000d3b  SPI0_IRQHandler                 
00000d3b  SPI1_IRQHandler                 
00000d3b  SVC_Handler                     
00000d3b  TIMA0_IRQHandler                
00000d3b  TIMA1_IRQHandler                
00000d3b  TIMG0_IRQHandler                
00000d3b  TIMG12_IRQHandler               
00000d3b  TIMG6_IRQHandler                
00000d3b  TIMG7_IRQHandler                
00000d3b  TIMG8_IRQHandler                
00000d3b  UART0_IRQHandler                
00000d3b  UART1_IRQHandler                
00000d3b  UART2_IRQHandler                
00000d3b  UART3_IRQHandler                
00000d3d  __TI_decompress_lzss            
00000db9  DL_I2C_fillControllerTXFIFO     
00000e17  __aeabi_idiv0                   
00000e19  SYSCFG_DL_I2C_MPU6050_init      
00000e6d  SYSCFG_DL_I2C_OLED_init         
00000ec1  SYSCFG_DL_SYSCTL_init           
00000f15  SYSCFG_DL_initPower             
00000f5d  scheduler_run                   
00000fa1  __aeabi_uidiv                   
00000fa1  __aeabi_uidivmod                
00000fe1  __TI_auto_init_nobinit_nopinit  
0000101d  mspm0_delay_ms                  
00001051  SYSCFG_DL_SYSTICK_init          
00001081  SYSCFG_DL_init                  
000010ad  _c_int00_noargs                 
000010d5  DL_I2C_setClockConfig           
000010fd  Get_Speed                       
00001121  oled_task                       
00001141  DL_Timer_setCaptCompUpdateMethod
0000115d  DL_Timer_setClockConfig         
00001179  main                            
00001195  DL_Timer_setCaptureCompareOutCtl
000011ad  Pid_Cal                         
000011c5  mspm0_get_clock_ms              
000011dd  __TI_zero_init_nomemset         
000011f5  GROUP1_IRQHandler               
00001209  __TI_decompress_none            
0000121d  DL_Timer_setCaptureCompareValue 
0000122d  SysTick_Handler                 
0000123d  led_task                        
00001249  scheduler_init                  
00001255  DL_Common_delayCycles           
00001261  __aeabi_memcpy                  
00001261  __aeabi_memcpy4                 
00001261  __aeabi_memcpy8                 
00001269  abort                           
0000126e  C$$EXIT                         
0000126f  HOSTexit                        
00001273  Reset_Handler                   
00001277  _system_pre_init                
00001280  asc2_1608                       
00001870  asc2_0806                       
00001ac8  __TI_Handler_Table_Base         
00001ad4  __TI_Handler_Table_Limit        
00001adc  __TI_CINIT_Base                 
00001aec  __TI_CINIT_Limit                
00001aec  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_DRIVEBackup                
202000bc  Coder_CounterA                  
202000c0  EncoderA                        
202000c4  start_time                      
202000c8  task_num                        
202000f0  sys_tick                        
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[114 symbols]
